#!/usr/bin/env node

/**
 * Test script to verify Electron MCP integration
 * This tests the IPC communication between main and renderer processes
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// Import the MCP setup from index.js
require('./src/index.js');

let testWindow;

function createTestWindow() {
  testWindow = new BrowserWindow({
    width: 800,
    height: 600,
    show: false, // Don't show the window during testing
    webPreferences: {
      preload: path.join(__dirname, 'src', 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // Load a minimal HTML page for testing
  testWindow.loadURL('data:text/html,<html><body><h1>MCP Test</h1></body></html>');

  return testWindow;
}

async function runTests() {
  console.log('🧪 Testing Electron MCP Integration...\n');

  try {
    // Wait for the app to be ready
    await app.whenReady();
    
    // Create test window
    const window = createTestWindow();
    
    // Wait for the window to load
    await new Promise(resolve => {
      window.webContents.once('did-finish-load', resolve);
    });

    console.log('✅ Electron app initialized');
    console.log('✅ Test window created');
    console.log('✅ Preload script loaded');

    // Test MCP client creation through IPC
    console.log('\n🔧 Testing MCP IPC handlers...');
    
    // Simulate IPC calls that would come from the renderer
    const clientId = await new Promise((resolve, reject) => {
      ipcMain.handleOnce('test:createClient', async () => {
        try {
          // This should trigger the mcp:createClient handler
          const result = await ipcMain.emit('mcp:createClient');
          return result;
        } catch (error) {
          reject(error);
        }
      });
      
      // Trigger the test
      window.webContents.send('test:createClient');
      
      // Set a timeout
      setTimeout(() => reject(new Error('Test timeout')), 5000);
    });

    if (clientId) {
      console.log('✅ MCP client created via IPC:', clientId);
    } else {
      console.log('⚠️  MCP client creation returned undefined');
    }

    console.log('\n🎉 Basic IPC communication test completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Start Smart Scribe normally: npm start');
    console.log('2. Open DevTools and check for errors');
    console.log('3. Try creating an MCP server in settings');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Clean up
    if (testWindow) {
      testWindow.close();
    }
    app.quit();
  }
}

// Handle app events
app.on('window-all-closed', () => {
  app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createTestWindow();
  }
});

// Run the tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
