const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Create a safe wrapper for MCP functionality
const mcpAPI = {
    // Create MCP client instance
    createClient: () => {
        console.log('Creating MCP client via IPC...');
        return ipcRenderer.invoke('mcp:createClient').catch(error => {
            console.error('Failed to create MCP client:', error);
            throw error;
        });
    },

    // Start MCP server
    startServer: (clientId, command, args) => {
        console.log(`Starting MCP server via IPC: ${command} ${args.join(' ')}`);
        return ipcRenderer.invoke('mcp:startServer', clientId, command, args).catch(error => {
            console.error('Failed to start MCP server:', error);
            throw error;
        });
    },

    // Stop MCP server
    stopServer: (clientId) => {
        console.log('Stopping MCP server via IPC');
        return ipcRenderer.invoke('mcp:stopServer', clientId).catch(error => {
            console.error('Failed to stop MCP server:', error);
            throw error;
        });
    },

    // Send request to MCP server
    sendRequest: (clientId, method, params) => {
        return ipcRenderer.invoke('mcp:sendRequest', clientId, method, params);
    },

    // Get tools from MCP server
    getTools: (clientId) => {
        return ipcRenderer.invoke('mcp:getTools', clientId);
    },

    // Call tool on MCP server
    callTool: (clientId, toolName, args) => {
        return ipcRenderer.invoke('mcp:callTool', clientId, toolName, args);
    },

    // Get client status
    getStatus: (clientId) => {
        return ipcRenderer.invoke('mcp:getStatus', clientId);
    },

    // Listen to MCP events
    onEvent: (clientId, eventName, callback) => {
        const channel = `mcp:event:${clientId}:${eventName}`;
        ipcRenderer.on(channel, callback);
        return () => ipcRenderer.removeListener(channel, callback);
    }
};

// Expose APIs to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
    // MCP API
    mcp: mcpAPI,

    // Platform information
    platform: process.platform,

    // Environment information
    env: {
        NODE_ENV: process.env.NODE_ENV || 'production'
    },

    // Legacy MCP Client for backward compatibility
    McpClient: function McpClientWrapper() {
        // Ensure this is called with 'new'
        if (!(this instanceof McpClientWrapper)) {
            return new McpClientWrapper();
        }

        this.clientId = Math.random().toString(36).substring(2, 11);
        this.events = new Map();
        this.isInitialized = false;

        // Initialize the client
        mcpAPI.createClient().then(id => {
            this.clientId = id;
            this.isInitialized = true;
        }).catch(error => {
            console.error('Failed to create MCP client:', error);
        });

        // Bind methods to maintain 'this' context
        this.startServer = async (command, args = []) => {
            if (!this.isInitialized) {
                await new Promise(resolve => {
                    const checkInit = () => {
                        if (this.isInitialized) {
                            resolve();
                        } else {
                            setTimeout(checkInit, 100);
                        }
                    };
                    checkInit();
                });
            }
            return mcpAPI.startServer(this.clientId, command, args);
        };

        this.stopServer = async () => {
            return mcpAPI.stopServer(this.clientId);
        };

        this.sendRequest = async (method, params) => {
            return mcpAPI.sendRequest(this.clientId, method, params);
        };

        this.getTools = async () => {
            return mcpAPI.getTools(this.clientId);
        };

        this.callTool = async (toolName, args) => {
            return mcpAPI.callTool(this.clientId, toolName, args);
        };

        this.getStatus = () => {
            return mcpAPI.getStatus(this.clientId);
        };

        this.on = (eventName, callback) => {
            if (!this.events.has(eventName)) {
                this.events.set(eventName, []);
            }
            this.events.get(eventName).push(callback);
            return mcpAPI.onEvent(this.clientId, eventName, callback);
        };

        this.emit = (eventName, ...args) => {
            const callbacks = this.events.get(eventName) || [];
            callbacks.forEach(callback => callback(...args));
        };

        return this;
    }
});
