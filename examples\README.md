# Smart Scribe MCP Server Examples

This directory contains example MCP (Model Context Protocol) servers that can be used with Smart Scribe to extend AI capabilities.

## Quick Start

### 1. Install Dependencies

```bash
cd examples
npm install
```

### 2. Test the Simple Text Server

```bash
# Test the server directly
node simple-mcp-server.js
```

### 3. Configure in Smart Scribe

1. Open Smart Scribe
2. Go to Settings (⚙️ or Alt+S)
3. Navigate to "MCP Server Settings"
4. Add a new server:
   - **Server Name**: Simple Text Server
   - **Command**: node
   - **Arguments**: /full/path/to/examples/simple-mcp-server.js
5. Click "🔍 Test Connection" to verify
6. Click "➕ Add Server" to save

## Available Servers

### Simple Text Server (`simple-mcp-server.js`)

A basic text analysis server that provides:

#### Tools Available:
- **word_count**: Count words, characters, lines, and paragraphs
- **text_stats**: Get readability statistics and complexity analysis
- **extract_keywords**: Find the most frequent words and phrases
- **sentiment_analysis**: Analyze emotional tone and sentiment
- **generate_summary**: Create extractive summaries

#### Example Usage:

**Direct Tool Calls:**
1. Select "🔧 Direct Tool Call" action
2. Choose "Simple Text Server" 
3. Select a tool (e.g., "word_count")
4. Provide arguments: `{"text": "Your text here"}`

**AI with Tools:**
1. Select "🤖 AI with Tools" action
2. Enter text like: "Analyze this text and provide statistics: [your text]"
3. The AI will automatically use appropriate tools

## Creating Your Own MCP Server

### Basic Structure

```javascript
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');

class MyCustomServer {
  constructor() {
    this.server = new Server({
      name: 'my-server',
      version: '1.0.0'
    }, {
      capabilities: { tools: {} }
    });
    
    this.setupHandlers();
  }

  setupHandlers() {
    // List available tools
    this.server.setRequestHandler('tools/list', async () => ({
      tools: [{
        name: 'my_tool',
        description: 'What this tool does',
        inputSchema: {
          type: 'object',
          properties: {
            input: { type: 'string', description: 'Input parameter' }
          },
          required: ['input']
        }
      }]
    }));

    // Handle tool calls
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;
      
      if (name === 'my_tool') {
        // Your tool logic here
        return {
          content: [{
            type: 'text',
            text: `Result: ${args.input}`
          }]
        };
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
  }
}

new MyCustomServer().run().catch(console.error);
```

### Tool Design Guidelines

1. **Clear Names**: Use descriptive tool names (e.g., `analyze_sentiment` not `analyze`)
2. **Good Descriptions**: Explain what the tool does and when to use it
3. **Proper Schemas**: Define input parameters clearly with types and descriptions
4. **Error Handling**: Return meaningful error messages
5. **Consistent Output**: Use consistent response formats

### Advanced Features

#### Resources
Provide data sources that AI can read:

```javascript
// List resources
this.server.setRequestHandler('resources/list', async () => ({
  resources: [{
    uri: 'file://data.json',
    name: 'Sample Data',
    description: 'Example dataset'
  }]
}));

// Read resources
this.server.setRequestHandler('resources/read', async (request) => {
  const { uri } = request.params;
  // Return resource content
});
```

#### Prompts
Provide reusable prompt templates:

```javascript
this.server.setRequestHandler('prompts/list', async () => ({
  prompts: [{
    name: 'analyze_text',
    description: 'Analyze text for key insights',
    arguments: [{
      name: 'text',
      description: 'Text to analyze'
    }]
  }]
}));
```

## Common Use Cases

### File Operations
- Read/write files
- List directories
- Process documents

### Web Integration
- Search the internet
- Fetch web pages
- API calls

### Data Processing
- Database queries
- CSV/JSON processing
- Statistical analysis

### Code Tools
- Execute code
- Lint/format code
- Generate documentation

### Communication
- Send emails
- Post to social media
- Webhook notifications

## Troubleshooting

### Server Won't Start
- Check Node.js version (requires 16+)
- Verify all dependencies are installed
- Check file paths and permissions

### Tools Not Appearing
- Ensure server status shows "connected"
- Check server logs for errors
- Try refreshing tools in Smart Scribe

### Permission Errors
- Run with appropriate permissions
- Check file system access rights
- Verify network connectivity for web tools

## Best Practices

1. **Security**: Validate all inputs and limit access scope
2. **Performance**: Keep tools fast and responsive
3. **Reliability**: Handle errors gracefully
4. **Documentation**: Provide clear descriptions and examples
5. **Testing**: Test tools thoroughly before deployment

## Contributing

To contribute new example servers:

1. Create a new `.js` file in this directory
2. Follow the naming convention: `[purpose]-mcp-server.js`
3. Include comprehensive documentation
4. Add usage examples
5. Test with Smart Scribe

## Resources

- [MCP Specification](https://modelcontextprotocol.io/)
- [MCP SDK Documentation](https://github.com/modelcontextprotocol/typescript-sdk)
- [Smart Scribe Documentation](../README.md)
- [Community Examples](https://github.com/modelcontextprotocol/servers)
