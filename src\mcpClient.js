const { spawn } = require('child_process');
const EventEmitter = require('events');

class McpClient extends EventEmitter {
    constructor() {
        super();
        this.serverProcess = null;
        this.messageBuffer = '';
        this.requestId = 0;
        this.pendingRequests = new Map();
        this.isConnected = false;
        this.isInitialized = false;
        this.serverCapabilities = null;
        this.tools = [];
        this.resources = [];
        this.connectionTimeout = 10000; // 10 seconds
        this.requestTimeout = 30000; // 30 seconds
    }

    /**
     * Starts the MCP server process.
     * @param {string} command The command to execute the MCP server.
     * @param {string[]} args Arguments for the command.
     * @returns {Promise<void>} A promise that resolves when the server is initialized.
     */
    async startServer(command, args = []) {
        if (this.serverProcess) {
            await this.stopServer();
        }

        return new Promise((resolve, reject) => {
            const connectionTimer = setTimeout(() => {
                this.stopServer();
                reject(new Error(`Connection timeout: MCP server failed to start within ${this.connectionTimeout}ms`));
            }, this.connectionTimeout);

            try {
                this.serverProcess = spawn(command, args, {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    env: { ...process.env, NODE_ENV: 'production' }
                });

                this.serverProcess.stdout.on('data', (data) => {
                    this.messageBuffer += data.toString();
                    this._processBuffer();
                });

                this.serverProcess.stderr.on('data', (data) => {
                    const errorMsg = data.toString().trim();
                    console.error(`MCP Server Error: ${errorMsg}`);
                    this.emit('error', `MCP Server Error: ${errorMsg}`);
                });

                this.serverProcess.on('close', (code) => {
                    console.log(`MCP Server process exited with code ${code}`);
                    this.isConnected = false;
                    this.isInitialized = false;
                    this.emit('disconnected', code);
                    this.serverProcess = null;
                    clearTimeout(connectionTimer);
                });

                this.serverProcess.on('error', (err) => {
                    console.error(`Failed to start MCP Server process: ${err.message}`);
                    this.isConnected = false;
                    this.isInitialized = false;
                    this.emit('error', `Failed to start MCP Server: ${err.message}`);
                    this.serverProcess = null;
                    clearTimeout(connectionTimer);
                    reject(err);
                });

                this.isConnected = true;
                this.emit('connected');
                console.log(`MCP Server started with command: ${command} ${args.join(' ')}`);

                // Send initialize message with proper MCP protocol
                this.sendRequest('initialize', {
                    protocolVersion: '2024-11-05',
                    capabilities: {
                        tools: {},
                        resources: {}
                    },
                    clientInfo: {
                        name: 'Smart Scribe',
                        version: '1.0.0'
                    }
                }).then(response => {
                    console.log('MCP Server initialized:', response);
                    this.isInitialized = true;
                    this.serverCapabilities = response.capabilities || {};
                    this.emit('initialized', response);
                    clearTimeout(connectionTimer);

                    // Automatically discover tools and resources
                    this._discoverCapabilities().then(() => {
                        resolve();
                    }).catch(error => {
                        console.warn('Failed to discover capabilities:', error);
                        resolve(); // Still resolve as the server is initialized
                    });
                }).catch(error => {
                    console.error('MCP Server initialization failed:', error);
                    this.emit('error', `MCP Server initialization failed: ${error.message}`);
                    clearTimeout(connectionTimer);
                    reject(error);
                });

            } catch (error) {
                console.error(`Error spawning MCP Server process: ${error.message}`);
                this.emit('error', `Error spawning MCP Server: ${error.message}`);
                this.serverProcess = null;
                clearTimeout(connectionTimer);
                reject(error);
            }
        });
    }

    /**
     * Stops the MCP server process.
     * @returns {Promise<void>} A promise that resolves when the server is stopped.
     */
    stopServer() {
        return new Promise((resolve) => {
            if (this.serverProcess) {
                const cleanup = () => {
                    this.serverProcess = null;
                    this.messageBuffer = '';
                    this.pendingRequests.clear();
                    this.isConnected = false;
                    this.isInitialized = false;
                    this.serverCapabilities = null;
                    this.tools = [];
                    this.resources = [];
                    this.emit('disconnected');
                    console.log('MCP Server stopped.');
                    resolve();
                };

                // Try graceful shutdown first
                if (this.isInitialized) {
                    this.sendRequest('shutdown', {}).then(() => {
                        this.serverProcess.kill('SIGTERM');
                        setTimeout(() => {
                            if (this.serverProcess) {
                                this.serverProcess.kill('SIGKILL');
                            }
                            cleanup();
                        }, 2000);
                    }).catch(() => {
                        this.serverProcess.kill('SIGKILL');
                        cleanup();
                    });
                } else {
                    this.serverProcess.kill('SIGKILL');
                    cleanup();
                }
            } else {
                resolve();
            }
        });
    }

    /**
     * Sends a JSON-RPC request to the MCP server.
     * @param {string} method The JSON-RPC method.
     * @param {object} params The parameters for the method.
     * @param {number} timeout Optional timeout in milliseconds.
     * @returns {Promise<object>} A promise that resolves with the response result.
     */
    sendRequest(method, params, timeout = this.requestTimeout) {
        return new Promise((resolve, reject) => {
            if (!this.serverProcess || !this.isConnected) {
                return reject(new Error('MCP Server not running or not connected.'));
            }

            const id = this.requestId++;
            const request = {
                jsonrpc: '2.0',
                id: id,
                method: method,
                params: params || {},
            };

            // Set up timeout
            const timeoutId = setTimeout(() => {
                this.pendingRequests.delete(id);
                reject(new Error(`Request timeout: ${method} took longer than ${timeout}ms`));
            }, timeout);

            this.pendingRequests.set(id, {
                resolve: (result) => {
                    clearTimeout(timeoutId);
                    resolve(result);
                },
                reject: (error) => {
                    clearTimeout(timeoutId);
                    reject(error);
                }
            });

            try {
                const requestStr = JSON.stringify(request) + '\n';
                this.serverProcess.stdin.write(requestStr);
                console.debug(`MCP Request sent: ${method}`, params);
            } catch (error) {
                this.pendingRequests.delete(id);
                clearTimeout(timeoutId);
                reject(new Error(`Failed to send request to MCP Server: ${error.message}`));
            }
        });
    }

    /**
     * Processes the incoming data buffer for complete JSON-RPC messages.
     * @private
     */
    _processBuffer() {
        let newlineIndex;
        while ((newlineIndex = this.messageBuffer.indexOf('\n')) !== -1) {
            const message = this.messageBuffer.substring(0, newlineIndex).trim();
            this.messageBuffer = this.messageBuffer.substring(newlineIndex + 1);

            if (message) {
                try {
                    const json = JSON.parse(message);
                    this._handleResponse(json);
                } catch (e) {
                    console.error('Failed to parse MCP Server response:', e, 'Message:', message);
                    this.emit('error', `Failed to parse MCP Server response: ${message}`);
                }
            }
        }
    }

    /**
     * Handles a parsed JSON-RPC response from the server.
     * @param {object} json The parsed JSON object.
     * @private
     */
    _handleResponse(json) {
        if (json.jsonrpc === '2.0' && typeof json.id !== 'undefined') {
            const request = this.pendingRequests.get(json.id);
            if (request) {
                this.pendingRequests.delete(json.id);
                if (json.result) {
                    request.resolve(json.result);
                } else if (json.error) {
                    request.reject(new Error(json.error.message || 'MCP Server error.'));
                }
            } else {
                console.warn('Received unsolicited response from MCP Server:', json);
            }
        } else if (json.jsonrpc === '2.0' && json.method) {
            // Handle notifications or server-initiated requests if any
            this.emit('notification', json.method, json.params);
        }
    }

    /**
     * Discovers and caches server capabilities (tools and resources).
     * @private
     */
    async _discoverCapabilities() {
        try {
            if (this.serverCapabilities?.tools) {
                this.tools = await this.getTools();
                console.log(`Discovered ${this.tools.length} tools:`, this.tools.map(t => t.name));
            }

            if (this.serverCapabilities?.resources) {
                this.resources = await this.getResources();
                console.log(`Discovered ${this.resources.length} resources:`, this.resources.map(r => r.uri));
            }

            this.emit('capabilitiesDiscovered', { tools: this.tools, resources: this.resources });
        } catch (error) {
            console.error('Failed to discover capabilities:', error);
            throw error;
        }
    }

    /**
     * Lists available tools from the MCP server.
     * @param {boolean} useCache Whether to use cached tools if available.
     * @returns {Promise<object[]>} A promise that resolves with an array of tools.
     */
    async getTools(useCache = true) {
        if (useCache && this.tools.length > 0) {
            return this.tools;
        }

        if (!this.isInitialized) {
            throw new Error('MCP Server not initialized');
        }

        const response = await this.sendRequest('tools/list', {});
        this.tools = response.tools || [];
        return this.tools;
    }

    /**
     * Gets detailed information about a specific tool.
     * @param {string} toolName The name of the tool.
     * @returns {object|null} Tool information or null if not found.
     */
    getToolInfo(toolName) {
        return this.tools.find(tool => tool.name === toolName) || null;
    }

    /**
     * Calls a specific tool on the MCP server with enhanced error handling.
     * @param {string} toolName The name of the tool to call.
     * @param {object} args Arguments for the tool.
     * @param {number} timeout Optional timeout for the tool call.
     * @returns {Promise<object>} A promise that resolves with the tool's response.
     */
    async callTool(toolName, args = {}, timeout = 60000) {
        if (!this.isInitialized) {
            throw new Error('MCP Server not initialized');
        }

        const toolInfo = this.getToolInfo(toolName);
        if (!toolInfo) {
            throw new Error(`Tool '${toolName}' not found. Available tools: ${this.tools.map(t => t.name).join(', ')}`);
        }

        console.log(`Calling MCP tool: ${toolName}`, args);

        try {
            const response = await this.sendRequest('tools/call', {
                name: toolName,
                arguments: args
            }, timeout);

            console.log(`MCP tool ${toolName} response:`, response);
            return response;
        } catch (error) {
            console.error(`MCP tool ${toolName} failed:`, error);
            throw new Error(`Tool '${toolName}' failed: ${error.message}`);
        }
    }

    /**
     * Lists available resources from the MCP server.
     * @param {boolean} useCache Whether to use cached resources if available.
     * @returns {Promise<object[]>} A promise that resolves with an array of resources.
     */
    async getResources(useCache = true) {
        if (useCache && this.resources.length > 0) {
            return this.resources;
        }

        if (!this.isInitialized) {
            throw new Error('MCP Server not initialized');
        }

        const response = await this.sendRequest('resources/list', {});
        this.resources = response.resources || [];
        return this.resources;
    }

    /**
     * Reads a specific resource from the MCP server.
     * @param {string} uri The URI of the resource to read.
     * @returns {Promise<object>} A promise that resolves with the resource's content.
     */
    async readResource(uri) {
        if (!this.isInitialized) {
            throw new Error('MCP Server not initialized');
        }

        console.log(`Reading MCP resource: ${uri}`);

        try {
            const response = await this.sendRequest('resources/read', { uri: uri });
            console.log(`MCP resource ${uri} read successfully`);
            return response;
        } catch (error) {
            console.error(`Failed to read MCP resource ${uri}:`, error);
            throw new Error(`Failed to read resource '${uri}': ${error.message}`);
        }
    }

    /**
     * Tests the connection to the MCP server.
     * @returns {Promise<boolean>} True if the server is responsive.
     */
    async testConnection() {
        try {
            await this.sendRequest('ping', {}, 5000);
            return true;
        } catch (error) {
            console.warn('MCP Server ping failed:', error);
            return false;
        }
    }

    /**
     * Gets the current status of the MCP client.
     * @returns {object} Status information.
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isInitialized: this.isInitialized,
            hasServerProcess: !!this.serverProcess,
            toolCount: this.tools.length,
            resourceCount: this.resources.length,
            capabilities: this.serverCapabilities
        };
    }
}

module.exports = McpClient;