{"name": "smart-scribe-mcp-examples", "version": "1.0.0", "description": "Example MCP servers for Smart Scribe AI writing assistant", "main": "simple-mcp-server.js", "scripts": {"start": "node simple-mcp-server.js", "install-deps": "npm install @modelcontextprotocol/sdk"}, "keywords": ["mcp", "model-context-protocol", "smart-scribe", "ai", "text-processing"], "author": "Smart Scribe Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0"}, "engines": {"node": ">=16.0.0"}}