const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('node:path');
const McpClient = require('./mcpClient');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Store MCP clients
const mcpClients = new Map();

// Function to setup MCP IPC handlers
function setupMcpHandlers() {
  console.log('Setting up MCP IPC handlers...');

  ipcMain.handle('mcp:createClient', () => {
    const clientId = Math.random().toString(36).substring(2, 11);
    const client = new McpClient();
    mcpClients.set(clientId, client);

    console.log(`Created MCP client: ${clientId}`);

    // Forward events to renderer
    client.on('connected', () => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send(`mcp:event:${clientId}:connected`);
      });
    });

    client.on('disconnected', (code) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send(`mcp:event:${clientId}:disconnected`, code);
      });
    });

    client.on('error', (error) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send(`mcp:event:${clientId}:error`, error);
      });
    });

    client.on('initialized', (response) => {
      BrowserWindow.getAllWindows().forEach(win => {
        win.webContents.send(`mcp:event:${clientId}:initialized`, response);
      });
    });

    return clientId;
  });

  ipcMain.handle('mcp:startServer', async (_, clientId, command, args) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    console.log(`Starting MCP server for client ${clientId}: ${command} ${args.join(' ')}`);
    return client.startServer(command, args);
  });

  ipcMain.handle('mcp:stopServer', async (_, clientId) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    console.log(`Stopping MCP server for client ${clientId}`);
    return client.stopServer();
  });

  ipcMain.handle('mcp:sendRequest', async (_, clientId, method, params) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    return client.sendRequest(method, params);
  });

  ipcMain.handle('mcp:getTools', async (_, clientId) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    return client.getTools();
  });

  ipcMain.handle('mcp:callTool', async (_, clientId, toolName, args) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    return client.callTool(toolName, args);
  });

  ipcMain.handle('mcp:getStatus', async (_, clientId) => {
    const client = mcpClients.get(clientId);
    if (!client) throw new Error(`MCP client ${clientId} not found`);
    return client.getStatus();
  });

  console.log('MCP IPC handlers registered successfully');
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false, // Disable node integration for security
      contextIsolation: true, // Enable context isolation for security
      enableRemoteModule: false, // Disable remote module for security
      webSecurity: true, // Enable web security
    },
  });

  // and load the index.html of the app.
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Setup MCP IPC handlers first
  setupMcpHandlers();

  // Then create the window
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  // Clean up MCP clients
  mcpClients.forEach(client => {
    try {
      client.stopServer();
    } catch (error) {
      console.error('Error stopping MCP client:', error);
    }
  });
  mcpClients.clear();

  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
