#!/usr/bin/env node

/**
 * Smart Scribe Startup Script
 * 
 * This script helps diagnose and start Smart Scribe with proper configuration
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Smart Scribe Startup Script\n');

// Check if we're in the right directory
const requiredFiles = [
    'package.json',
    'src/index.js',
    'src/index.html',
    'src/renderer.js',
    'src/mcpClient.js'
];

console.log('📁 Checking project structure...');
for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - MISSING`);
        console.log('\n❌ Please run this script from the Smart Scribe project root directory');
        process.exit(1);
    }
}

// Check if node_modules exists
if (fs.existsSync('node_modules')) {
    console.log('✅ node_modules');
} else {
    console.log('❌ node_modules - MISSING');
    console.log('\n📦 Installing dependencies...');
    
    const npmInstall = spawn('npm', ['install'], { stdio: 'inherit' });
    
    npmInstall.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dependencies installed successfully');
            startApplication();
        } else {
            console.log('❌ Failed to install dependencies');
            process.exit(1);
        }
    });
    
    return;
}

function startApplication() {
    console.log('\n🎯 Starting Smart Scribe...');
    
    // Set environment for development
    process.env.NODE_ENV = 'development';
    
    const electronProcess = spawn('npm', ['start'], { 
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: 'development' }
    });
    
    electronProcess.on('close', (code) => {
        console.log(`\n📱 Smart Scribe exited with code ${code}`);
    });
    
    electronProcess.on('error', (error) => {
        console.error('❌ Failed to start Smart Scribe:', error.message);
        console.log('\n💡 Try running: npm install');
        process.exit(1);
    });
}

// Start the application
startApplication();
