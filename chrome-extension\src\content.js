// Content Script for Smart Scribe Extension
console.log('Smart Scribe Content Script Loaded');

// State
let smartScribeWidget = null;
let currentTextArea = null;
let isProcessing = false;

// Initialize content script
function initializeContentScript() {
  // Listen for text selection with comprehensive event handling
  document.addEventListener('mouseup', handleTextSelection);
  document.addEventListener('keyup', handleTextSelection);
  document.addEventListener('selectionchange', handleTextSelection);
  
  // Listen for focus on text areas and input fields
  document.addEventListener('focusin', handleTextFieldFocus);
  document.addEventListener('focusout', handleTextFieldBlur);
  
  // Additional event listeners for better text selection detection
  document.addEventListener('mousedown', hideQuickActionButton);
  document.addEventListener('touchend', handleTextSelection);
  
  // Watch for dynamically added content
  observeForNewTextElements();
  
  console.log('Smart Scribe: Content script initialized');
}

// Handle text selection
function handleTextSelection(event) {
  // Small delay to ensure selection is complete
  setTimeout(() => {
    // Don't handle if clicking on Smart Scribe elements
    if (event && event.target && (event.target.closest('.smart-scribe-widget') || event.target.id?.includes('smart-scribe'))) {
      return;
    }
    
    const selection = window.getSelection();
    let selectedText = '';
    
    // Handle different types of selections
    if (selection && selection.rangeCount > 0) {
      selectedText = selection.toString().trim();
      
      // If no text from window.getSelection(), try to get text from input/textarea
      if (!selectedText && event && event.target) {
        selectedText = getSelectedTextFromInput(event.target);
      }
    }
    
    // Also check for text selected in input fields and textareas
    if (!selectedText && document.activeElement) {
      selectedText = getSelectedTextFromInput(document.activeElement);
    }
    
    // Show quick action if we have enough text
    if (selectedText && selectedText.length > 10) {
      // For input/textarea selections, use the element's bounding rect
      if (document.activeElement && isTextInputElement(document.activeElement)) {
        const rect = document.activeElement.getBoundingClientRect();
        showQuickActionButtonForInput(selectedText, rect);
      } else {
        showQuickActionButton(selection, selectedText);
      }
    } else {
      hideQuickActionButton();
    }
  }, 10);
}

// Handle text field focus
function handleTextFieldFocus(event) {
  const element = event.target;
  
  // Check if it's a text input element
  if (isTextInputElement(element)) {
    currentTextArea = element;
    showTextFieldDropdown(element);
  }
}

// Handle text field blur
function handleTextFieldBlur(event) {
  // Delay hiding to allow clicking on widget
  setTimeout(() => {
    if (!document.querySelector('.smart-scribe-widget:hover')) {
      hideTextFieldWidget();
      currentTextArea = null;
    }
  }, 200);
}

// Check if element is a text input
function isTextInputElement(element) {
  if (!element) return false;
  
  const tagName = element.tagName.toLowerCase();
  const type = element.type?.toLowerCase();
  
  return (
    tagName === 'textarea' ||
    (tagName === 'input' && ['text', 'email', 'search', 'url', 'password', 'tel', 'number'].includes(type)) ||
    element.contentEditable === 'true' ||
    element.isContentEditable ||
    element.getAttribute('contenteditable') === 'true' ||
    element.getAttribute('contenteditable') === '' ||
    tagName === 'div' && (element.contentEditable === 'true' || element.isContentEditable) ||
    element.hasAttribute('data-text-editor') ||
    element.classList.contains('editor') ||
    element.classList.contains('text-editor') ||
    element.classList.contains('contenteditable') ||
    // Rich text editors
    element.classList.contains('ql-editor') || // Quill
    element.classList.contains('CodeMirror') || // CodeMirror
    element.classList.contains('ace_editor') || // Ace Editor
    element.closest('[data-slate-editor]') || // Slate
    element.closest('.tox-edit-area') || // TinyMCE
    element.closest('.cke_editable') || // CKEditor
    element.closest('[data-testid="tweetTextarea_0"]') || // Twitter
    element.closest('[aria-label*="tweet"]') || // Twitter variations
    element.closest('[data-text="true"]') || // Various social media
    element.role === 'textbox' ||
    element.getAttribute('aria-multiline') === 'true'
  );
}

// Get selected text from input/textarea elements
function getSelectedTextFromInput(element) {
  if (!element || !isTextInputElement(element)) {
    return '';
  }
  
  // For regular input/textarea elements
  if (element.tagName.toLowerCase() === 'input' || element.tagName.toLowerCase() === 'textarea') {
    const start = element.selectionStart;
    const end = element.selectionEnd;
    
    if (start !== undefined && end !== undefined && start !== end) {
      return element.value.substring(start, end).trim();
    }
  }
  
  // For contentEditable elements, try to get selected text from window.getSelection()
  if (element.contentEditable === 'true' || element.isContentEditable) {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      if (element.contains(range.commonAncestorContainer) || element === range.commonAncestorContainer) {
        return selection.toString().trim();
      }
    }
  }
  
  return '';
}

// Show quick action button for input field selections
function showQuickActionButtonForInput(selectedText, rect) {
  hideQuickActionButton();
  
  const button = document.createElement('div');
  button.id = 'smart-scribe-quick-action';
  button.className = 'smart-scribe-widget';
  button.innerHTML = `
    <div class="smart-scribe-button">
      <span class="icon">✨</span>
      <span class="text">Smart Scribe</span>
    </div>
  `;
  
  // Position the button
  button.style.cssText = `
    position: fixed;
    top: ${rect.bottom + 10}px;
    left: ${rect.left}px;
    z-index: 99999;
    background: #2563eb;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    pointer-events: auto;
  `;
  
  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.background = '#1d4ed8';
    button.style.transform = 'translateY(-2px)';
  });
  
  button.addEventListener('mouseleave', () => {
    button.style.background = '#2563eb';
    button.style.transform = 'translateY(0)';
  });
  
  // Handle click
  button.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    showActionMenu(selectedText, rect);
    hideQuickActionButton();
  }, true);
  
  document.body.appendChild(button);
}

// Show quick action button for selected text
function showQuickActionButton(selection, selectedText) {
  hideQuickActionButton();
  
  if (!selection || selection.rangeCount === 0) {
    return;
  }
  
  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  
  // Use provided selectedText or get from selection
  const textToUse = selectedText || selection.toString().trim();
  
  const button = document.createElement('div');
  button.id = 'smart-scribe-quick-action';
  button.className = 'smart-scribe-widget';
  button.innerHTML = `
    <div class="smart-scribe-button">
      <span class="icon">✨</span>
      <span class="text">Smart Scribe</span>
    </div>
  `;
  
  // Position the button
  button.style.cssText = `
    position: fixed;
    top: ${rect.bottom + window.scrollY + 10}px;
    left: ${rect.left + window.scrollX}px;
    z-index: 99999;
    background: #2563eb;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    pointer-events: auto;
  `;
  
  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.background = '#1d4ed8';
    button.style.transform = 'translateY(-2px)';
  });
  
  button.addEventListener('mouseleave', () => {
    button.style.background = '#2563eb';
    button.style.transform = 'translateY(0)';
  });
  
  // Handle click - use stored selectedText instead of selection.toString()
  button.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    showActionMenu(textToUse, rect);
    hideQuickActionButton();
  }, true); // Use capture phase
  
  document.body.appendChild(button);
}

// Hide quick action button
function hideQuickActionButton() {
  const existing = document.getElementById('smart-scribe-quick-action');
  if (existing) {
    existing.remove();
  }
}

// Show text field dropdown
function showTextFieldDropdown(element) {
  hideTextFieldWidget();

  const rect = element.getBoundingClientRect();

  const widget = document.createElement('div');
  widget.id = 'smart-scribe-text-widget';
  widget.className = 'smart-scribe-widget';
  widget.innerHTML = `
    <div class="smart-scribe-text-dropdown" style="position: relative; display: inline-block; min-width: 42px;">
      <button class="dropdown-toggle" style="
        background-color: #2563eb;
        color: white;
        padding: 10px;
        border: none;
        cursor: pointer;
        width: 42px;
        height: 42px;
        text-align: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">✨</button>
      <div class="dropdown-content" style="
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        overflow: hidden;
        margin-top: 2px;
        min-width: 200px;
      ">
        <button class="action-btn" data-action="improve-style" style="
          color: #374151;
          padding: 12px 16px;
          border: none;
          background: none;
          cursor: pointer;
          width: 100%;
          text-align: left;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          display: block;
        ">✨ Improve Style</button>
        <button class="action-btn" data-action="fix-grammar" style="
          color: #374151;
          padding: 12px 16px;
          border: none;
          background: none;
          cursor: pointer;
          width: 100%;
          text-align: left;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          display: block;
        ">📝 Fix Grammar</button>
        <button class="action-btn" data-action="shorten" style="
          color: #374151;
          padding: 12px 16px;
          border: none;
          background: none;
          cursor: pointer;
          width: 100%;
          text-align: left;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          display: block;
        ">📏 Shorten</button>
        <button class="action-btn" data-action="expand" style="
          color: #374151;
          padding: 12px 16px;
          border: none;
          background: none;
          cursor: pointer;
          width: 100%;
          text-align: left;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          display: block;
        ">📈 Expand</button>
        <button class="action-btn" data-action="translate" style="
          color: #374151;
          padding: 12px 16px;
          border: none;
          background: none;
          cursor: pointer;
          width: 100%;
          text-align: left;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
          display: block;
        ">🌐 Translate</button>
      </div>
    </div>
  `;
  // Position the widget - use simpler positioning
  widget.style.cssText = `
    position: fixed;
    top: ${rect.bottom + 10}px;
    left: ${rect.left}px;
    z-index: 999999;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    overflow: visible;
  `;

  
  document.body.appendChild(widget);
  
  // Add dropdown functionality after widget is appended
  const dropdownToggle = widget.querySelector('.dropdown-toggle');
  const dropdownContent = widget.querySelector('.dropdown-content');
  const dropdown = widget.querySelector('.smart-scribe-text-dropdown');
  
  // Toggle dropdown on button click
  dropdownToggle.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (dropdownContent.style.display === 'block') {
      dropdownContent.style.display = 'none';
    } else {
      dropdownContent.style.cssText = `
        display: block !important;
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        width: 200px !important;
        background-color: white !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 6px !important;
        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.15) !important;
        z-index: 999999 !important;
        margin-top: 2px !important;
        visibility: visible !important;
        opacity: 1 !important;
      `;
    }
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function closeDropdown(e) {
    if (!widget.contains(e.target)) {
      dropdownContent.style.display = 'none';
      document.removeEventListener('click', closeDropdown);
    }
  });
  
  // Add hover effects for action buttons
  widget.querySelectorAll('.action-btn').forEach(btn => {
    btn.addEventListener('mouseenter', () => {
      btn.style.backgroundColor = '#f3f4f6';
      btn.style.color = '#2563eb';
    });
    btn.addEventListener('mouseleave', () => {
      btn.style.backgroundColor = 'transparent';
      btn.style.color = '#374151';
    });
    btn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      const action = e.target.dataset.action;
      dropdownContent.style.display = 'none';
      processTextFieldContent(element, action);
    });
  });
}

// Hide text field widget
function hideTextFieldWidget() {
  const existing = document.getElementById('smart-scribe-text-widget');
  if (existing) {
    existing.remove();
  }
}

// Show action menu for selected text
function showActionMenu(selectedText, rect) {
  const menu = document.createElement('div');
  menu.id = 'smart-scribe-action-menu';
  menu.className = 'smart-scribe-widget';
  menu.innerHTML = `
    <div class="smart-scribe-menu">
      <div class="menu-header">Smart Scribe Actions</div>
      <button class="menu-action" data-action="improve-style">✨ Improve Style</button>
      <button class="menu-action" data-action="fix-grammar">📝 Fix Grammar</button>
      <button class="menu-action" data-action="shorten">📏 Shorten</button>
      <button class="menu-action" data-action="expand">📈 Expand</button>
      <button class="menu-action" data-action="translate">🌐 Translate</button>
      <div class="menu-divider"></div>
      <button class="menu-action open-popup">🚀 Open Smart Scribe</button>
    </div>
  `;
  
  // Position the menu
  menu.style.cssText = `
    position: fixed;
    top: ${rect.bottom + window.scrollY + 10}px;
    left: ${rect.left + window.scrollX}px;
    z-index: 10000;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    overflow: hidden;
  `;
  
  // Add menu styles
  const menuStyle = document.createElement('style');
  menuStyle.textContent = `
    .smart-scribe-menu {
      padding: 8px;
    }
    
    .smart-scribe-menu .menu-header {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 12px;
      font-weight: 600;
      color: #6b7280;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 8px 12px 4px;
      margin-bottom: 4px;
    }
    
    .smart-scribe-menu .menu-action {
      width: 100%;
      padding: 10px 12px;
      border: none;
      background: none;
      text-align: left;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      color: #374151;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .smart-scribe-menu .menu-action:hover {
      background: #f3f4f6;
      color: #2563eb;
    }
    
    .smart-scribe-menu .menu-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 8px 0;
    }
    
    .smart-scribe-menu .open-popup {
      color: #059669;
    }
    
    .smart-scribe-menu .open-popup:hover {
      background: #ecfdf5;
      color: #047857;
    }
  `;
  
  if (!document.getElementById('smart-scribe-menu-styles')) {
    menuStyle.id = 'smart-scribe-menu-styles';
    document.head.appendChild(menuStyle);
  }
  
  // Add event listeners
  menu.querySelectorAll('.menu-action:not(.open-popup)').forEach(btn => {
    btn.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      processSelectedText(selectedText, action);
      hideActionMenu();
    });
  });
  
  menu.querySelector('.open-popup').addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openPopup' });
    hideActionMenu();
  });
  
  // Close menu when clicking outside
  setTimeout(() => {
    document.addEventListener('click', function closeMenu(e) {
      if (!menu.contains(e.target)) {
        hideActionMenu();
        document.removeEventListener('click', closeMenu);
      }
    });
  }, 100);
  
  document.body.appendChild(menu);
}

// Hide action menu
function hideActionMenu() {
  const existing = document.getElementById('smart-scribe-action-menu');
  if (existing) {
    existing.remove();
  }
}

// Process selected text
async function processSelectedText(text, action) {
  if (isProcessing) return;
  
  isProcessing = true;
  showProcessingIndicator('Processing selected text...');
  
  try {
    // Check if extension context is still valid
    if (!chrome.runtime?.id) {
      throw new Error('Extension was reloaded. Please refresh the page to use Smart Scribe.');
    }
    
    // Get settings from storage
    const settings = await new Promise((resolve, reject) => {
      try {
        chrome.storage.sync.get([
          'ai_provider', 'ai_model', 'language', 'tone', 'openrouter_api_key'
        ], (result) => {
          if (chrome.runtime.lastError) {
            reject(new Error('Extension context invalidated. Please refresh the page.'));
          } else {
            resolve(result);
          }
        });
      } catch (e) {
        reject(new Error('Extension context invalidated. Please refresh the page.'));
      }
    });
    
    if (!settings.ai_model) {
      throw new Error('Please select an AI model in the extension settings first.');
    }
    
    // Send message to background script
    const response = await new Promise((resolve, reject) => {
      try {
        chrome.runtime.sendMessage({
          action: 'processText',
          text: text,
          actionType: action,
          settings: settings
        }, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error('Extension context invalidated. Please refresh the page.'));
          } else {
            resolve(response);
          }
        });
      } catch (e) {
        reject(new Error('Extension context invalidated. Please refresh the page.'));
      }
    });
    
    if (response && response.success) {
      replaceSelectedText(response.result);
      showSuccessIndicator('Text refined successfully!');
    } else {
      throw new Error(response?.error || 'Unknown error occurred');
    }
    
  } catch (error) {
    console.error('Error processing text:', error);
    if (error.message.includes('context invalidated') || error.message.includes('Extension was reloaded')) {
      showErrorIndicator('Please refresh the page after extension reload');
    } else {
      showErrorIndicator('Error: ' + error.message);
    }
  } finally {
    isProcessing = false;
    hideProcessingIndicator();
  }
}

// Process text field content
async function processTextFieldContent(element, action) {
  const text = getTextFromElement(element);
  if (!text.trim()) {
    showErrorIndicator('No text to process');
    return;
  }
  
  if (isProcessing) return;
  
  isProcessing = true;
  showProcessingIndicator('Processing text...');
  
  try {
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get([
        'ai_provider', 'ai_model', 'language', 'tone', 'openrouter_api_key'
      ], resolve);
    });
    
    if (!settings.ai_model) {
      throw new Error('Please select an AI model in the extension settings first.');
    }
    
    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'processText',
        text: text,
        actionType: action,
        settings: settings
      }, resolve);
    });
    
    if (response.success) {
      setTextToElement(element, response.result);
      showSuccessIndicator('Text refined successfully!');
    } else {
      throw new Error(response.error);
    }
    
  } catch (error) {
    console.error('Error processing text:', error);
    showErrorIndicator('Error: ' + error.message);
  } finally {
    isProcessing = false;
    hideProcessingIndicator();
  }
}

// Utility functions
function getTextFromElement(element) {
  if (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input') {
    return element.value;
  } else if (element.contentEditable === 'true' || element.isContentEditable) {
    return element.textContent || element.innerText;
  }
  return '';
}

function setTextToElement(element, text) {
  if (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input') {
    element.value = text;
    element.dispatchEvent(new Event('input', { bubbles: true }));
  } else if (element.contentEditable === 'true' || element.isContentEditable) {
    element.textContent = text;
    element.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

function replaceSelectedText(newText) {
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(newText));
    selection.removeAllRanges();
  }
}

function showProcessingIndicator(message) {
  hideProcessingIndicator();
  
  const indicator = document.createElement('div');
  indicator.id = 'smart-scribe-processing';
  indicator.innerHTML = `
    <div style="display: flex; align-items: center; gap: 8px;">
      <div style="width: 16px; height: 16px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
      ${message}
    </div>
  `;
  
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2563eb;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
}

function hideProcessingIndicator() {
  const existing = document.getElementById('smart-scribe-processing');
  if (existing) {
    existing.remove();
  }
}

function showSuccessIndicator(message) {
  const indicator = document.createElement('div');
  indicator.textContent = '✓ ' + message;
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #059669;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
  
  setTimeout(() => {
    indicator.remove();
  }, 3000);
}

function showErrorIndicator(message) {
  const indicator = document.createElement('div');
  indicator.textContent = '⚠ ' + message;
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc2626;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
  `;
  
  document.body.appendChild(indicator);
  
  setTimeout(() => {
    indicator.remove();
  }, 4000);
}

// Observe for dynamically added text elements
function observeForNewTextElements() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node or its children are text elements
            if (isTextInputElement(node)) {
              // New text element detected, no need to do anything special
              // Our event listeners will handle focus automatically
            }
            
            // Check children
            const textElements = node.querySelectorAll && node.querySelectorAll(
              'input[type="text"], input[type="email"], input[type="search"], input[type="url"], ' +
              'textarea, [contenteditable="true"], [contenteditable=""], ' +
              '.ql-editor, .CodeMirror, .ace_editor, [data-slate-editor], ' +
              '.tox-edit-area, .cke_editable, [role="textbox"]'
            );
            
            if (textElements && textElements.length > 0) {
              // New text elements found, our event listeners will handle them
            }
          }
        });
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}
