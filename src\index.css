/* Modern UI Styles for <PERSON> Scribe */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Modern Color Palette */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #6b7280;
  --success-color: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;

  /* Neutral Colors - Simplified */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-focus: #2563eb;

  /* Modern Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-secondary);
  min-height: 100vh;
  overflow-x: hidden;
  padding: var(--spacing-4);
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 2rem);
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

/* Header Styles - Simplified */
.app-header {
  background-color: var(--bg-primary);
  color: var(--gray-800);
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--border-light);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.logo-icon {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
}

.app-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
  color: var(--gray-900);
}

.app-subtitle {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
  margin-left: var(--spacing-2);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  background-color: var(--bg-tertiary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
}

.status-dot {
  width: 4px;
  height: 4px;
  background-color: var(--success-color);
  border-radius: 50%;
}

.status-text {
  font-size: 10px;
  font-weight: 600;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  background-color: var(--bg-secondary);
}

/* Quick Controls Section */
.quick-controls-section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.quick-controls-panel {
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-4);
}

.quick-controls-left {
  display: flex;
  gap: var(--spacing-3);
  flex: 1;
}

.quick-controls-right {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  min-width: 120px;
}

/* AI with Tools Info */
.ai-tools-info {
  background: linear-gradient(135deg, #dbeafe 0%, #e0f2fe 100%);
  border: 1px solid #93c5fd;
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin-top: var(--spacing-2);
  display: none;
}

.ai-tools-info .info-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.ai-tools-info .info-icon {
  font-size: 1.2em;
  flex-shrink: 0;
}

.ai-tools-info .info-text {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  line-height: 1.4;
}

.control-label {
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  font-size: var(--font-size-base);
}

/* Settings Button */
.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  background-color: var(--bg-tertiary);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 40px;
}

.settings-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
}

.settings-icon {
  font-size: var(--font-size-base);
}

.control-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  cursor: pointer;
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 14px;
  padding-right: var(--spacing-8);
}

.control-select:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.control-select:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Model Select Container */
.model-select-container {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
  width: 100%;
}

.model-select-container .setting-select {
  flex: 1;
  min-width: 0; /* Allow shrinking */
}

/* Model Actions */
.model-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-2);
  flex-wrap: wrap;
}

.set-default-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--bg-tertiary);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.set-default-btn:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.set-default-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.set-default-btn .button-icon {
  font-size: var(--font-size-sm);
}

.default-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.default-indicator .indicator-icon {
  font-size: var(--font-size-sm);
}

.refresh-models-btn {
  padding: var(--spacing-2);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.refresh-models-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
  transform: rotate(90deg);
}

.refresh-models-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.refresh-models-btn.loading {
  animation: spin 1s linear infinite;
}

/* API Key Section */
.api-key-section {
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  margin-top: var(--spacing-4);
  transition: all var(--transition-normal);
}

.api-key-section.show {
  display: block !important;
  animation: slideDown var(--transition-normal) ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.api-key-container {
  position: relative;
  display: flex;
  align-items: center;
}

.api-key-input {
  flex: 1;
  padding: var(--spacing-3) var(--spacing-4);
  padding-right: var(--spacing-10);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
}

.api-key-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.api-key-input::placeholder {
  color: var(--gray-400);
  font-style: italic;
}

.toggle-visibility-btn {
  position: absolute;
  right: var(--spacing-2);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
  font-size: var(--font-size-base);
}

.toggle-visibility-btn:hover {
  background-color: var(--gray-100);
}

.api-key-help {
  display: block;
  margin-top: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  line-height: 1.4;
}

.api-key-help a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.api-key-help a:hover {
  text-decoration: underline;
}

/* Settings Modal */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.settings-modal.active {
  opacity: 1;
  visibility: visible;
}

.settings-modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 95%;
  max-width: 550px;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95) translateY(20px);
  transition: transform var(--transition-normal);
  border: 1px solid var(--border-light);
  margin: var(--spacing-4);
  padding-bottom: var(--spacing-4);
}

.settings-modal.active .settings-modal-content {
  transform: scale(1) translateY(0);
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.settings-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-800);
}

.settings-title-icon {
  font-size: var(--font-size-xl);
}

.close-settings-btn {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-settings-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.settings-body {
  padding: var(--spacing-4);
  max-height: 70vh;
  overflow-y: auto;
}

/* Custom scrollbar for settings body */
.settings-body::-webkit-scrollbar {
  width: 6px;
}

.settings-body::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
}

.settings-body::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-sm);
}

.settings-body::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

.settings-section {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-3);
  padding-bottom: var(--spacing-2);
  border-bottom: 1px solid var(--border-light);
}

.section-icon {
  font-size: var(--font-size-xl);
}

.settings-grid {
  display: grid;
  gap: var(--spacing-5);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  width: 100%;
  min-width: 0; /* Allow shrinking */
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  font-size: var(--font-size-base);
  color: var(--gray-700);
}

.setting-select,
.setting-input {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  transition: all var(--transition-fast);
  width: 100%;
  box-sizing: border-box;
}

.setting-select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 14px;
  padding-right: var(--spacing-6);
}

.setting-select:hover,
.setting-input:hover {
  border-color: var(--border-medium);
  background-color: var(--bg-tertiary);
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.setting-description {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  line-height: 1.4;
}

.setting-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.setting-link:hover {
  text-decoration: underline;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-left: auto;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-slider:hover {
  background-color: var(--gray-400);
}

input:checked + .toggle-slider:hover {
  background-color: var(--primary-hover);
}

/* Settings Footer */
.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-5) var(--spacing-4);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
}

.reset-settings-btn,
.save-settings-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.reset-settings-btn {
  background-color: var(--gray-200);
  color: var(--gray-700);
  margin-bottom: var(--spacing-3);
}

.reset-settings-btn:hover {
  background-color: var(--gray-300);
  color: var(--gray-800);
}

.save-settings-btn {
  background-color: var(--primary-color);
  color: white;
  margin-bottom: var(--spacing-3);
}

.save-settings-btn:hover {
  background-color: var(--primary-hover);
}

/* Refine Button - Simplified */
.refine-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

.refine-button:hover {
  background-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
}

.refine-button:active {
  transform: translateY(1px);
}

.refine-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  font-size: var(--font-size-lg);
  transition: transform var(--transition-fast);
}

.button-text {
  font-weight: 600;
}

.button-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.refine-button.loading .button-loader {
  opacity: 1;
}

.refine-button.loading .button-icon,
.refine-button.loading .button-text {
  opacity: 0;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Text Section */
.text-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.text-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  flex: 1;
  min-height: 0;
}

.text-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: box-shadow var(--transition-fast);
}

.text-panel:hover {
  box-shadow: var(--shadow-md);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-light);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.panel-icon {
  font-size: var(--font-size-lg);
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-button:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
  border-color: var(--border-medium);
}

.copy-button.copied {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.char-counter {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  font-weight: 500;
}

/* Textarea Container */
.textarea-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

textarea {
  flex: 1;
  padding: var(--spacing-4);
  border: none;
  border-radius: 0;
  font-size: var(--font-size-base);
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  resize: none;
  outline: none;
  transition: all var(--transition-fast);
  min-height: 280px;
}

textarea::placeholder {
  color: var(--gray-400);
}

textarea:focus {
  background-color: var(--bg-secondary);
}

#input-text {
  border-left: 3px solid var(--primary-color);
}

#output-text {
  border-left: 3px solid var(--success-color);
  background-color: var(--bg-secondary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(4px);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--gray-600);
  text-align: center;
}

/* Input Panel Specific Styles */
.input-panel {
  border-top: 2px solid var(--primary-color);
}

/* Output Panel Specific Styles */
.output-panel {
  border-top: 2px solid var(--success-color);
}

/* Custom Prompts Section */
.custom-prompts-section {
  margin-bottom: var(--spacing-6);
}

.custom-prompts-panel {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.custom-prompts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
}

.section-icon {
  font-size: 1.25rem;
}

.custom-prompts-actions {
  display: flex;
  gap: var(--spacing-2);
}

.primary-button {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
}

.primary-button:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-1px);
}

.secondary-button {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
}

.secondary-button:hover {
  background-color: var(--gray-200);
  transform: translateY(-1px);
}

.custom-prompt-editor {
  display: grid;
  gap: var(--spacing-4);
}

.prompt-selector {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.prompt-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.custom-prompt-textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  line-height: 1.5;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  resize: vertical;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.custom-prompt-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.prompt-variables {
  display: flex;
  justify-content: flex-end;
}

.variable-hint {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    padding: var(--spacing-6);
    gap: var(--spacing-6);
  }

  .quick-controls-panel {
    flex-direction: column;
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    width: 100%;
    justify-content: space-between;
  }

  .quick-controls-right {
    width: 100%;
    justify-content: center;
  }

  .text-panels {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .app-header {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .app-title {
    font-size: var(--font-size-2xl);
  }

  .settings-modal-content {
    width: 98%;
    max-width: 500px;
    max-height: 85vh;
    margin: var(--spacing-2);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-4);
    gap: var(--spacing-4);
  }

  .quick-controls-left {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .control-group {
    min-width: auto;
  }

  .panel-header {
    padding: var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .panel-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  textarea {
    padding: var(--spacing-4);
    min-height: 200px;
  }

  .app-container {
    box-shadow: none;
  }

  .settings-modal-content {
    width: 100%;
    max-width: none;
    max-height: 95vh;
    margin: 0;
    border-radius: 0;
  }

  .settings-header {
    padding: var(--spacing-4);
  }

  .settings-body {
    padding: var(--spacing-3);
  }

  .model-actions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .set-default-btn {
    width: 100%;
    justify-content: center;
  }

  .settings-footer {
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-4) var(--spacing-3);
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  body {
    padding: var(--spacing-2);
  }

  .app-header {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .main-content {
    padding: var(--spacing-3);
    gap: var(--spacing-3);
  }

  .quick-controls-panel {
    padding: var(--spacing-3);
  }

  .refine-button {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
    min-height: 40px;
  }

  .app-title {
    font-size: var(--font-size-xl);
  }

  .app-subtitle {
    display: none;
  }

  .app-container {
    border-radius: var(--radius-lg);
    min-height: calc(100vh - 1rem);
  }
}

/* Prompts Management Modal */
.prompts-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
}

.empty-prompts-message {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.empty-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: var(--spacing-2);
}

.prompt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.prompt-item:hover {
  background-color: var(--gray-50);
}

.prompt-item:last-child {
  border-bottom: none;
}

.prompt-item.selected {
  background-color: var(--primary-50);
  border-color: var(--primary-200);
}

.prompt-info {
  flex: 1;
}

.prompt-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
}

.prompt-meta {
  display: flex;
  gap: var(--spacing-2);
  font-size: 0.75rem;
  color: var(--gray-500);
}

.prompt-category {
  background-color: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.prompt-actions {
  display: flex;
  gap: var(--spacing-1);
}

.prompt-action-btn {
  padding: var(--spacing-1);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.prompt-action-btn:hover {
  background-color: var(--gray-200);
}

.setting-textarea {
  width: 100%;
  min-height: 120px;
  padding: var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  line-height: 1.5;
  background-color: var(--bg-primary);
  color: var(--gray-800);
  resize: vertical;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.setting-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.footer-right {
  display: flex;
  gap: var(--spacing-2);
}

.delete-prompt-btn {
  background-color: var(--red-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-prompt-btn:hover {
  background-color: var(--red-700);
}

.cancel-prompt-btn {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-prompt-btn:hover {
  background-color: var(--gray-200);
}

.save-prompt-btn {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.save-prompt-btn:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
}

/* Batch Processing Styles */
.batch-button {
  background: linear-gradient(135deg, var(--orange-500), var(--orange-600));
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-height: 44px;
}

.batch-button:hover {
  background: linear-gradient(135deg, var(--orange-600), var(--orange-700));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

.batch-button:active {
  transform: translateY(0);
}

.batch-modal-content {
  max-width: 900px;
  max-height: 90vh;
}

.batch-input-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.batch-input-controls {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.batch-texts-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
}

.empty-batch-message {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.batch-text-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
  gap: var(--spacing-3);
}

.batch-text-item:last-child {
  border-bottom: none;
}

.batch-text-item.processing {
  background-color: var(--blue-50);
  border-left: 3px solid var(--blue-500);
}

.batch-text-item.completed {
  background-color: var(--green-50);
  border-left: 3px solid var(--green-500);
}

.batch-text-item.failed {
  background-color: var(--red-50);
  border-left: 3px solid var(--red-500);
}

.batch-text-content {
  flex: 1;
  min-width: 0;
}

.batch-text-preview {
  font-size: 0.875rem;
  color: var(--gray-700);
  line-height: 1.4;
  margin-bottom: var(--spacing-1);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.batch-text-status {
  font-size: 0.75rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.batch-text-actions {
  display: flex;
  gap: var(--spacing-1);
  flex-shrink: 0;
}

.batch-text-action-btn {
  padding: var(--spacing-1);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.batch-text-action-btn:hover {
  background-color: var(--gray-200);
}

.batch-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.batch-custom-prompt-section {
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--border-light);
}

.batch-progress-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-3);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-800);
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  min-width: 40px;
  text-align: right;
}

.batch-status {
  font-size: 0.875rem;
  color: var(--gray-600);
  text-align: center;
  padding: var(--spacing-2);
  background-color: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.export-results-btn {
  background-color: var(--green-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.export-results-btn:hover {
  background-color: var(--green-700);
}

.start-batch-btn {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.start-batch-btn:hover {
  background: linear-gradient(135deg, var(--green-700), var(--green-800));
}

.start-batch-btn:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
}

/* Text Comparison Styles */
.compare-button {
  background-color: var(--blue-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.compare-button:hover {
  background-color: var(--blue-700);
  transform: translateY(-1px);
}

.comparison-modal-content {
  max-width: 1200px;
  max-height: 95vh;
  width: 95vw;
}

.comparison-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.comparison-control-group {
  display: flex;
  align-items: center;
}

.comparison-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  user-select: none;
}

.comparison-toggle input[type="checkbox"] {
  display: none;
}

.comparison-toggle .toggle-slider {
  width: 40px;
  height: 20px;
  background-color: var(--gray-300);
  border-radius: 10px;
  position: relative;
  transition: background-color 0.2s ease;
}

.comparison-toggle .toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
}

.comparison-toggle input[type="checkbox"]:checked + .toggle-slider {
  background-color: var(--primary-500);
}

.comparison-toggle input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 0.875rem;
  color: var(--gray-700);
  font-weight: 500;
}

.comparison-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-3);
}

.stat-card {
  background-color: var(--gray-50);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  text-align: center;
}

.stat-card .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--spacing-1);
}

.stat-card .stat-label {
  font-size: 0.75rem;
  color: var(--gray-600);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.comparison-panels {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  min-height: 400px;
}

.comparison-panel {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--bg-primary);
}

.comparison-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--border-light);
}

.comparison-panel-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: 0;
}

.comparison-panel-actions {
  display: flex;
  gap: var(--spacing-1);
}

.comparison-action-btn {
  padding: var(--spacing-1);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.comparison-action-btn:hover {
  background-color: var(--gray-200);
}

.comparison-panel-content {
  padding: var(--spacing-3);
  max-height: 400px;
  overflow-y: auto;
}

.comparison-text {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Diff highlighting */
.diff-added {
  background-color: var(--green-100);
  color: var(--green-800);
  padding: 1px 2px;
  border-radius: 2px;
}

.diff-removed {
  background-color: var(--red-100);
  color: var(--red-800);
  padding: 1px 2px;
  border-radius: 2px;
  text-decoration: line-through;
}

.diff-changed {
  background-color: var(--yellow-100);
  color: var(--yellow-800);
  padding: 1px 2px;
  border-radius: 2px;
}

.unified-diff-container {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  max-height: 300px;
  overflow-y: auto;
}

.unified-diff {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  padding: var(--spacing-3);
  white-space: pre-wrap;
}

.diff-line {
  display: block;
  padding: 2px 4px;
  margin: 1px 0;
}

.diff-line-added {
  background-color: var(--green-50);
  color: var(--green-800);
  border-left: 3px solid var(--green-500);
}

.diff-line-removed {
  background-color: var(--red-50);
  color: var(--red-800);
  border-left: 3px solid var(--red-500);
}

.diff-line-context {
  color: var(--gray-600);
}

.export-comparison-btn {
  background-color: var(--purple-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.export-comparison-btn:hover {
  background-color: var(--purple-700);
}

/* AI Model Recommendations Styles */
.recommend-model-btn {
  background-color: var(--purple-600);
  color: white;
  border: none;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recommend-model-btn:hover {
  background-color: var(--purple-700);
  transform: translateY(-1px);
}

.model-recommendation {
  margin-top: var(--spacing-4);
  border: 1px solid var(--purple-200);
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--purple-50), var(--blue-50));
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommendation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3);
  background: linear-gradient(135deg, var(--purple-100), var(--blue-100));
  border-bottom: 1px solid var(--purple-200);
}

.recommendation-icon {
  font-size: 1.25rem;
  margin-right: var(--spacing-2);
}

.recommendation-title {
  font-weight: 600;
  color: var(--purple-800);
  flex: 1;
}

.close-recommendation-btn {
  background: none;
  border: none;
  color: var(--purple-600);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.close-recommendation-btn:hover {
  background-color: var(--purple-200);
}

.recommendation-content {
  padding: var(--spacing-4);
}

.recommended-model {
  margin-bottom: var(--spacing-4);
}

.recommended-model-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--purple-800);
  margin-bottom: var(--spacing-1);
}

.recommended-model-reason {
  font-size: 0.875rem;
  color: var(--purple-600);
  font-style: italic;
}

.recommendation-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.text-analysis,
.model-benefits {
  background-color: white;
  border: 1px solid var(--purple-100);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
}

.text-analysis h4,
.model-benefits h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--purple-700);
  margin: 0 0 var(--spacing-2) 0;
}

.analysis-item,
.benefit-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-1);
  font-size: 0.875rem;
  color: var(--gray-700);
}

.analysis-item:last-child,
.benefit-item:last-child {
  margin-bottom: 0;
}

.analysis-icon,
.benefit-icon {
  font-size: 0.75rem;
  width: 16px;
  text-align: center;
}

.recommendation-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.apply-recommendation-btn {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.apply-recommendation-btn:hover {
  background: linear-gradient(135deg, var(--green-700), var(--green-800));
}

.dismiss-recommendation-btn {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--border-light);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.dismiss-recommendation-btn:hover {
  background-color: var(--gray-200);
}

/* Context-aware Processing Styles */
.contextual-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, var(--blue-500), var(--purple-600));
  color: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
  max-width: 350px;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-content {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-4);
  gap: var(--spacing-3);
}

.notification-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: var(--spacing-1);
}

.notification-reason {
  font-size: 0.75rem;
  opacity: 0.9;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Template System Styles */
.templates-button {
  background: linear-gradient(135deg, var(--green-500), var(--green-600));
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-height: 44px;
}

.templates-button:hover {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.templates-modal-content {
  max-width: 1000px;
  max-height: 90vh;
  width: 90vw;
}

.template-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.template-category-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.template-category-btn:hover {
  background-color: var(--gray-50);
  border-color: var(--primary-300);
}

.template-category-btn.active {
  background-color: var(--primary-50);
  border-color: var(--primary-500);
  color: var(--primary-700);
}

.category-icon {
  font-size: 1.25rem;
}

.category-name {
  flex: 1;
  font-weight: 500;
  font-size: 0.875rem;
}

.category-count {
  background-color: var(--gray-200);
  color: var(--gray-600);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.template-category-btn.active .category-count {
  background-color: var(--primary-200);
  color: var(--primary-700);
}

.template-search {
  position: relative;
  margin-bottom: var(--spacing-4);
}

.template-search-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 40px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.template-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: 0.875rem;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-4);
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-2);
}

.template-card {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.template-card:hover {
  border-color: var(--primary-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-card.selected {
  border-color: var(--primary-500);
  background-color: var(--primary-50);
}

.template-card-header {
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--border-light);
}

.template-card-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
  font-size: 0.875rem;
}

.template-card-category {
  display: inline-block;
  background-color: var(--gray-100);
  color: var(--gray-600);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.template-card-body {
  padding: var(--spacing-3);
}

.template-card-description {
  font-size: 0.75rem;
  color: var(--gray-600);
  line-height: 1.4;
  margin-bottom: var(--spacing-2);
}

.template-card-preview {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-style: italic;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.template-preview {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  overflow: hidden;
}

.template-preview-header {
  padding: var(--spacing-4);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--border-light);
}

.template-preview-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--spacing-1) 0;
}

.template-preview-description {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin: 0 0 var(--spacing-2) 0;
  line-height: 1.4;
}

.template-preview-meta {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.template-category-tag {
  background-color: var(--primary-100);
  color: var(--primary-700);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.template-variables-info {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.template-preview-content {
  padding: var(--spacing-4);
  max-height: 200px;
  overflow-y: auto;
}

.template-preview-text {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--gray-700);
  white-space: pre-wrap;
  background-color: var(--gray-50);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.template-variables {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-light);
  background-color: var(--gray-50);
}

.variables-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0 0 var(--spacing-3) 0;
}

.variables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.variable-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.variable-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-700);
}

.variable-input {
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.variable-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.template-preview-actions {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.use-template-btn {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.use-template-btn:hover {
  background: linear-gradient(135deg, var(--green-700), var(--green-800));
}

.customize-template-btn {
  background-color: var(--blue-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.customize-template-btn:hover {
  background-color: var(--blue-700);
}

.create-template-btn {
  background-color: var(--purple-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.create-template-btn:hover {
  background-color: var(--purple-700);
}

.template-variable {
  background-color: var(--yellow-100);
  color: var(--yellow-800);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  border: 1px solid var(--yellow-300);
}

.empty-templates-message {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  grid-column: 1 / -1;
}

.empty-templates-message .empty-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: var(--spacing-2);
}

/* Template Creator Styles */
.template-creator-preview {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--gray-50);
  padding: var(--spacing-3);
  min-height: 120px;
}

.detected-variables-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  background-color: var(--gray-50);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  min-height: 60px;
}

.variable-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  background-color: var(--blue-100);
  color: var(--blue-800);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--blue-200);
}

.variable-tag-icon {
  font-size: 0.625rem;
}

.no-variables-message {
  color: var(--gray-500);
  font-style: italic;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 40px;
}

/* Enhanced User Experience Styles */

/* History Controls */
.history-controls {
  display: flex;
  gap: var(--spacing-1);
}

.history-button {
  background-color: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--border-light);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-button:hover:not(:disabled) {
  background-color: var(--gray-200);
  transform: translateY(-1px);
}

.history-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.history-icon {
  font-size: 1rem;
  font-weight: bold;
}

/* Import Button */
.import-button {
  background-color: var(--blue-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.import-button:hover {
  background-color: var(--blue-700);
  transform: translateY(-1px);
}

/* Stats Button */
.stats-button {
  background-color: var(--purple-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.stats-button:hover {
  background-color: var(--purple-700);
  transform: translateY(-1px);
}

/* Export Button */
.export-button {
  background-color: var(--green-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.export-button:hover {
  background-color: var(--green-700);
  transform: translateY(-1px);
}

/* Text Statistics Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-3);
}

.complexity-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.complexity-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.complexity-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.complexity-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-600);
}

.complexity-bar {
  width: 100%;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.complexity-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--green-500), var(--yellow-500), var(--red-500));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  width: 0%;
}

.text-analysis-results {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.analysis-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.analysis-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.analysis-content {
  font-size: 0.875rem;
  color: var(--gray-600);
  padding: var(--spacing-2);
  background-color: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.refresh-stats-btn {
  background-color: var(--blue-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.refresh-stats-btn:hover {
  background-color: var(--blue-700);
}

/* Export Options Styles */
.export-formats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.format-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-option:hover {
  background-color: var(--gray-50);
  border-color: var(--primary-300);
}

.format-option.selected {
  background-color: var(--primary-50);
  border-color: var(--primary-500);
}

.format-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.format-info {
  flex: 1;
}

.format-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
}

.format-description {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.export-option {
  display: flex;
  align-items: center;
}

.export-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  user-select: none;
}

.export-toggle input[type="checkbox"] {
  display: none;
}

.start-export-btn {
  background: linear-gradient(135deg, var(--green-600), var(--green-700));
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.start-export-btn:hover {
  background: linear-gradient(135deg, var(--green-700), var(--green-800));
}

.start-export-btn:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
}

/* Drag and Drop Styles */
.textarea-container {
  position: relative;
}

.drag-drop-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed var(--primary-300);
  border-radius: var(--radius-lg);
  background-color: rgba(99, 102, 241, 0.1);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;
}

.drag-drop-area.drag-over {
  border-color: var(--primary-500);
  background-color: rgba(99, 102, 241, 0.2);
  transform: scale(1.02);
}

.drag-drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  pointer-events: none;
}

.drag-drop-icon {
  font-size: 3rem;
  color: var(--primary-500);
}

.drag-drop-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-700);
}

.drag-drop-hint {
  font-size: 0.875rem;
  color: var(--primary-600);
}

/* Productivity Features Styles */

/* Documents Button */
.documents-button {
  background: linear-gradient(135deg, var(--orange-500), var(--orange-600));
  color: white;
  border: none;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-height: 44px;
}

.documents-button:hover {
  background: linear-gradient(135deg, var(--orange-600), var(--orange-700));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(251, 146, 60, 0.3);
}

/* Save Document Button */
.save-document-button {
  background-color: var(--green-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.save-document-button:hover {
  background-color: var(--green-700);
  transform: translateY(-1px);
}

/* Document Management Modal */
.documents-modal-content {
  max-width: 1200px;
  max-height: 90vh;
  width: 95vw;
}

/* Workspace Controls */
.workspace-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.workspace-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.control-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  min-width: 100px;
}

.control-select {
  flex: 1;
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
}

.manage-btn {
  background-color: var(--gray-100);
  border: 1px solid var(--border-light);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.manage-btn:hover {
  background-color: var(--gray-200);
}

/* Search Controls */
.search-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.search-input-group {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) 40px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: 0.875rem;
}

.filter-controls {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.filter-select {
  padding: var(--spacing-2);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  min-width: 120px;
}

.clear-filters-btn {
  background-color: var(--gray-100);
  border: 1px solid var(--border-light);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.clear-filters-btn:hover {
  background-color: var(--gray-200);
}

/* Quick Actions */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-3);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.quick-action-btn:hover {
  background-color: var(--gray-50);
  border-color: var(--primary-300);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 1.5rem;
}

.action-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

/* Document Count */
.document-count {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: normal;
  margin-left: var(--spacing-2);
}

/* Documents List */
.documents-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
}

.empty-documents-message {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.empty-documents-message .empty-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: var(--spacing-2);
}

/* Document Cards */
.document-card {
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-4);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.document-card:hover {
  background-color: var(--gray-50);
}

.document-card.selected {
  background-color: var(--primary-50);
  border-left: 4px solid var(--primary-500);
}

.document-card:last-child {
  border-bottom: none;
}

.document-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2);
}

.document-title {
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
  font-size: 1rem;
  flex: 1;
}

.document-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: 0.75rem;
  color: var(--gray-500);
}

.document-favorite {
  color: var(--yellow-500);
  font-size: 0.875rem;
}

.document-workspace {
  background-color: var(--blue-100);
  color: var(--blue-700);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.document-description {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
  line-height: 1.4;
}

.document-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-2);
}

.document-tag {
  background-color: var(--gray-100);
  color: var(--gray-600);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.document-preview {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-style: italic;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: var(--spacing-2);
}

.document-actions {
  display: flex;
  gap: var(--spacing-2);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.document-card:hover .document-actions {
  opacity: 1;
}

.document-action-btn {
  background: none;
  border: 1px solid var(--border-light);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.document-action-btn:hover {
  background-color: var(--gray-100);
}

.document-action-btn.edit {
  color: var(--blue-600);
  border-color: var(--blue-300);
}

.document-action-btn.delete {
  color: var(--red-600);
  border-color: var(--red-300);
}

.document-action-btn.favorite {
  color: var(--yellow-600);
  border-color: var(--yellow-300);
}

/* Save Document Modal */
.save-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.save-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  user-select: none;
}

.save-option input[type="checkbox"] {
  margin: 0;
}

.option-text {
  font-size: 0.875rem;
  color: var(--gray-700);
}

.delete-selected-btn {
  background-color: var(--red-600);
  color: white;
  border: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.delete-selected-btn:hover {
  background-color: var(--red-700);
}

/* Responsive Design */
@media (max-width: 768px) {
  .workspace-controls {
    flex-direction: column;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .documents-modal-content {
    width: 95vw;
    max-width: none;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Better Accessibility */
button:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* MCP Server Configuration Styles */
.settings-subsection {
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.settings-subsection-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-3);
}

.subsection-icon {
  font-size: 1em;
}

.count-badge {
  background: var(--primary-color);
  color: white;
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  margin-left: auto;
}

.help-button {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: 2px;
  border-radius: var(--radius-sm);
  margin-left: auto;
  transition: all 0.2s ease;
}

.help-button:hover {
  color: var(--primary-color);
  background: var(--primary-light);
}

/* MCP Templates Grid */
.mcp-templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.mcp-template-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.mcp-template-btn:hover {
  border-color: var(--primary-color);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.template-icon {
  font-size: 2em;
  margin-bottom: var(--spacing-1);
}

.template-name {
  font-weight: 600;
  color: var(--gray-800);
  font-size: var(--font-size-sm);
}

.template-desc {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  line-height: 1.3;
}

/* Settings Actions */
.settings-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
  justify-content: flex-end;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-6);
  color: var(--gray-500);
}

.empty-icon {
  font-size: 3em;
  margin-bottom: var(--spacing-3);
  display: block;
}

.empty-title {
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-700);
}

.empty-description {
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

/* Enhanced MCP Server List */
.mcp-servers-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mcp-server-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  transition: all 0.2s ease;
}

.mcp-server-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.mcp-server-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.mcp-server-name {
  font-weight: 600;
  color: var(--gray-800);
}

.mcp-server-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  padding: 2px 8px;
  border-radius: var(--radius-full);
}

.mcp-server-status.connected {
  background: #dcfce7;
  color: #166534;
}

.mcp-server-status.connecting {
  background: #fef3c7;
  color: #92400e;
}

.mcp-server-status.disconnected {
  background: #fee2e2;
  color: #991b1b;
}

.mcp-server-status.error {
  background: #fee2e2;
  color: #991b1b;
}

.mcp-server-details {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-3);
}

.mcp-server-tools {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-3);
}

.tool-badge {
  background: var(--primary-light);
  color: var(--primary-color);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.mcp-server-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.mcp-server-actions .action-button {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #000000;
    --border-medium: #000000;
    --gray-400: #000000;
    --gray-500: #000000;
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;

    --bg-primary: #1e293b;
    --bg-secondary: #0f172a;
    --bg-tertiary: #334155;

    --border-light: #334155;
    --border-medium: #475569;
  }

  body {
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  }
}