// Background Service Worker for Smart Scribe Extension
console.log('Smart Scribe Background Service Worker Loaded');

// Extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Smart Scribe Extension Installed:', details.reason);
  
  // Create context menu items
  createContextMenus();
  
  // Set default settings if first install
  if (details.reason === 'install') {
    chrome.storage.sync.set({
      ai_provider: 'ollama',
      language: 'English',
      tone: 'professional',
      action: 'improve-style'
    });
  }
});

// Create context menu items
function createContextMenus() {
  // Remove existing menus first
  chrome.contextMenus.removeAll(() => {
    // Main menu
    chrome.contextMenus.create({
      id: 'smart-scribe-main',
      title: 'Smart Scribe',
      contexts: ['selection']
    });
    
    // Sub-menu items
    chrome.contextMenus.create({
      id: 'improve-style',
      parentId: 'smart-scribe-main',
      title: '✨ Improve Style',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'fix-grammar',
      parentId: 'smart-scribe-main',
      title: '📝 Fix Grammar',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'shorten',
      parentId: 'smart-scribe-main',
      title: '📏 Shorten',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'expand',
      parentId: 'smart-scribe-main',
      title: '📈 Expand',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'translate',
      parentId: 'smart-scribe-main',
      title: '🌐 Translate',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'separator',
      parentId: 'smart-scribe-main',
      type: 'separator',
      contexts: ['selection']
    });
    
    chrome.contextMenus.create({
      id: 'open-popup',
      parentId: 'smart-scribe-main',
      title: '🚀 Open Smart Scribe',
      contexts: ['selection']
    });
  });
}

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  console.log('Context menu clicked:', info.menuItemId);
  
  if (info.menuItemId === 'open-popup') {
    // Open the extension popup
    chrome.action.openPopup();
    return;
  }
  
  // Handle text processing actions
  if (['improve-style', 'fix-grammar', 'shorten', 'expand', 'translate'].includes(info.menuItemId)) {
    handleContextMenuAction(info, tab);
  }
});

// Handle context menu text processing
async function handleContextMenuAction(info, tab) {
  const selectedText = info.selectionText;
  if (!selectedText) return;
  
  try {
    // Get current settings
    const settings = await new Promise((resolve) => {
      chrome.storage.sync.get([
        'ai_provider', 'ai_model', 'language', 'tone', 'openrouter_api_key'
      ], resolve);
    });
    
    if (!settings.ai_model) {
      // Show notification to set up model
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'Smart Scribe',
        message: 'Please select an AI model in the extension settings first.'
      });
      return;
    }
    
    // Inject content script to show processing indicator
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: showProcessingIndicator,
      args: [selectedText]
    });
    
    // Process the text
    const processedText = await processTextInBackground(
      selectedText, 
      info.menuItemId, 
      settings
    );
    
    // Inject content script to replace text
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: replaceSelectedText,
      args: [processedText]
    });
    
  } catch (error) {
    console.error('Error processing text:', error);
    
    // Show error notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon-48.png',
      title: 'Smart Scribe Error',
      message: 'Failed to process text: ' + error.message
    });
  }
}

// Process text in background
async function processTextInBackground(text, action, settings) {
  const prompt = createPromptForAction(text, action, settings);
  
  if (settings.ai_provider === 'ollama') {
    return await processWithOllamaBackground(prompt, settings.ai_model);
  } else if (settings.ai_provider === 'openrouter') {
    return await processWithOpenRouterBackground(prompt, settings.ai_model, settings.openrouter_api_key);
  }
  
  throw new Error('No AI provider configured');
}

// Create prompt for specific action
function createPromptForAction(text, action, settings) {
  const tone = settings.tone || 'professional';
  const language = settings.language || 'English';
  
  if (action === 'translate') {
    return `Translate the following text to ${language}. Only return the translated text without any explanations or additional comments:\n\n${text}`;
  }
  
  const actionPrompts = {
    'improve-style': 'Improve the writing style and clarity of',
    'fix-grammar': 'Fix grammar, spelling, and punctuation errors in',
    'shorten': 'Make this text more concise and shorter',
    'expand': 'Expand and elaborate on'
  };
  
  const actionText = actionPrompts[action] || 'Improve';
  
  return `${actionText} the following text. Make it sound ${tone} and ensure it's in ${language}. Only return the improved text without any explanations or additional comments:\n\n${text}`;
}

// Ollama processing
async function processWithOllamaBackground(prompt, model) {
  const response = await fetch('http://localhost:11434/api/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      model: model,
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.7,
        max_tokens: 1000
      }
    })
  });
  
  if (!response.ok) {
    throw new Error('Ollama API error: ' + response.statusText);
  }
  
  const data = await response.json();
  return data.response.trim();
}

// OpenRouter processing
async function processWithOpenRouterBackground(prompt, model, apiKey) {
  if (!apiKey) {
    throw new Error('OpenRouter API key is required');
  }
  
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': chrome.runtime.getURL(''),
      'X-Title': 'Smart Scribe Extension'
    },
    body: JSON.stringify({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 1000
    })
  });
  
  if (!response.ok) {
    throw new Error('OpenRouter API error: ' + response.statusText);
  }
  
  const data = await response.json();
  return data.choices[0].message.content.trim();
}

// Content script functions (injected into web pages)
function showProcessingIndicator(selectedText) {
  // Create and show a temporary indicator
  const indicator = document.createElement('div');
  indicator.id = 'smart-scribe-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2563eb;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 8px;
  `;
  
  indicator.innerHTML = `
    <div style="width: 16px; height: 16px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
    Smart Scribe is processing...
  `;
  
  // Add spin animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
  
  document.body.appendChild(indicator);
  
  // Store original selection for replacement
  window.smartScribeSelection = window.getSelection().toString();
}

function replaceSelectedText(newText) {
  // Remove processing indicator
  const indicator = document.getElementById('smart-scribe-indicator');
  if (indicator) {
    indicator.remove();
  }
  
  // Try to replace the selected text
  const selection = window.getSelection();
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    range.deleteContents();
    range.insertNode(document.createTextNode(newText));
    selection.removeAllRanges();
  }
  
  // Show success notification
  const successIndicator = document.createElement('div');
  successIndicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #059669;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
  `;
  
  successIndicator.textContent = '✓ Text refined by Smart Scribe';
  document.body.appendChild(successIndicator);
  
  // Remove success indicator after 3 seconds
  setTimeout(() => {
    successIndicator.remove();
  }, 3000);
}

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup automatically due to the action.default_popup in manifest
  console.log('Extension icon clicked');
});

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background received message:', request);
  
  if (request.action === 'processText') {
    processTextInBackground(request.text, request.actionType, request.settings)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }
});

console.log('Smart Scribe Background Service Worker Ready');
